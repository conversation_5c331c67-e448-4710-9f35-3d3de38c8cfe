"""插画生成Agent的LangGraph工作流"""

import os
from dotenv import load_dotenv
from langgraph.graph import StateGraph, START, END
from langchain_core.runnables import RunnableConfig

from agent.state import IllustrationState
from agent.configuration import Configuration
from agent.illustration_nodes import (
    input_handler,
    story_splitter,
    character_extractor,
    character_image_generator,
    storyboard_generator,
    scene_prompt_optimizer,
    image_generator,
    unified_image_generator,
    image_merger,
    finalize_illustration
)

load_dotenv()


def serialize_messages(messages):
    """
    将AIMessage对象序列化为可 JSON化的格式
    
    Args:
        messages: 消息列表，可能包含AIMessage对象
        
    Returns:
        序列化后的消息列表
    """
    if not messages:
        return []
        
    serialized = []
    for msg in messages:
        if hasattr(msg, 'content'):
            # AIMessage对象
            serialized.append({"content": str(msg.content)})
        elif isinstance(msg, dict):
            # 已经是字典格式
            serialized.append(msg)
        else:
            # 其他类型，转为字符串
            serialized.append({"content": str(msg)})
    
    return serialized


def serialize_result(result):
    """
    序列化插画生成结果，确保所有内容都可以JSON化

    Args:
        result: 插画生成结果

    Returns:
        序列化后的结果
    """
    if not isinstance(result, dict):
        return result

    serialized_result = result.copy()

    # 移除不可序列化的字段
    if 'progress_callback' in serialized_result:
        del serialized_result['progress_callback']

    # 序列化messages字段
    if 'messages' in serialized_result:
        serialized_result['messages'] = serialize_messages(serialized_result['messages'])

    return serialized_result


def should_generate_character_images(state: IllustrationState) -> str:
    """
    条件路由：判断是否需要生成角色基准图
    
    Args:
        state: 当前状态
        
    Returns:
        下一个节点名称
    """
    characters = state.get("characters", [])
    main_character = state.get("main_character")
    
    # 如果有主角或重要角色，生成基准图
    if main_character or any(char.get("role") == "main" for char in characters):
        return "character_image_generator"
    else:
        return "storyboard_generator"


def should_continue_to_generation(state: IllustrationState) -> str:
    """
    条件路由：判断是否继续到图像生成

    Args:
        state: 当前状态

    Returns:
        下一个节点名称
    """
    scene_prompts = state.get("scene_prompts", [])

    # 检查是否有有效的场景提示词
    if scene_prompts and len(scene_prompts) > 0:
        return "unified_image_generator"
    else:
        return "finalize_illustration"


def should_merge_images(state: IllustrationState) -> str:
    """
    条件路由：判断是否需要合并图像
    
    Args:
        state: 当前状态
        
    Returns:
        下一个节点名称
    """
    generated_images = state.get("generated_images", [])
    
    # 检查是否有成功生成的图片
    successful_images = [img for img in generated_images if img.get("success", False)]
    
    if len(successful_images) > 1:
        return "image_merger"
    else:
        return "finalize_illustration"


# 创建插画生成Agent图
def create_illustration_graph():
    """
    创建插画生成Agent的LangGraph工作流
    
    Returns:
        编译后的图实例
    """
    # 创建状态图
    builder = StateGraph(IllustrationState, config_schema=Configuration)
    
    # 添加所有节点
    builder.add_node("input_handler", input_handler)
    builder.add_node("story_splitter", story_splitter)
    builder.add_node("character_extractor", character_extractor)
    builder.add_node("character_image_generator", character_image_generator)
    builder.add_node("storyboard_generator", storyboard_generator)
    builder.add_node("scene_prompt_optimizer", scene_prompt_optimizer)
    builder.add_node("image_generator", image_generator)
    builder.add_node("unified_image_generator", unified_image_generator)
    builder.add_node("image_merger", image_merger)
    builder.add_node("finalize_illustration", finalize_illustration)
    
    # 设置入口点
    builder.add_edge(START, "input_handler")
    
    # 构建工作流
    # 1. 输入处理 -> 故事分段
    builder.add_edge("input_handler", "story_splitter")
    
    # 2. 故事分段 -> 角色提取
    builder.add_edge("story_splitter", "character_extractor")
    
    # 3. 角色提取 -> 条件分支（是否生成角色基准图）
    builder.add_conditional_edges(
        "character_extractor",
        should_generate_character_images,
        {
            "character_image_generator": "character_image_generator",
            "storyboard_generator": "storyboard_generator"
        }
    )
    
    # 4. 角色基准图生成 -> 分镜生成
    builder.add_edge("character_image_generator", "storyboard_generator")
    
    # 5. 分镜生成 -> 提示词优化
    builder.add_edge("storyboard_generator", "scene_prompt_optimizer")
    
    # 6. 提示词优化 -> 条件分支（是否继续生成图像）
    builder.add_conditional_edges(
        "scene_prompt_optimizer",
        should_continue_to_generation,
        {
            "unified_image_generator": "unified_image_generator",
            "finalize_illustration": "finalize_illustration"
        }
    )

    # 7. 统一图像生成 -> 条件分支（是否合并图像）
    builder.add_conditional_edges(
        "unified_image_generator",
        should_merge_images,
        {
            "image_merger": "image_merger",
            "finalize_illustration": "finalize_illustration"
        }
    )
    
    # 8. 图像合并 -> 最终化
    builder.add_edge("image_merger", "finalize_illustration")
    
    # 9. 最终化 -> 结束
    builder.add_edge("finalize_illustration", END)
    
    # 编译图
    graph = builder.compile(name="illustration-generation-agent")
    
    return graph


# 创建图实例
illustration_graph = create_illustration_graph()


# 便捷函数：运行插画生成
async def generate_illustration(
    user_input: str,
    style_preference: str = "anime",
    num_panels: int = 4,
    config: RunnableConfig = None,
    progress_callback: callable = None,
    multi_edit_model_id: str = "flux3dc-cff2-4177-ad3a-28d9b4d3ff48"
) -> dict:
    """
    运行插画生成工作流
    
    Args:
        user_input: 用户输入的故事内容
        style_preference: 风格偏好
        num_panels: 分镜数量
        config: 运行配置
        progress_callback: 进度回调函数
        
    Returns:
        生成结果
    """
    # 构建初始状态
    initial_state = {
        "user_input": user_input,
        "style_preference": style_preference,
        "num_panels": num_panels,
        "multi_edit_model_id": multi_edit_model_id,
        "messages": [],
        "story_segments": [],
        "storyboards": [],
        "characters": [],
        "character_base_images": [],
        "scene_prompts": [],
        "generated_images": [],
        "main_character": None,
        "final_illustration": None,
        "processed_story": "",
        "story_type": "",
        "reasoning_model": "qwen-max-0403",
        "progress_callback": progress_callback  # 添加到状态中
    }
    
    # 运行图
    try:
        result = await illustration_graph.ainvoke(initial_state, config=config)
        print(f"DEBUG: 工作流原始结果键: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        if isinstance(result, dict) and "error" in result:
            print(f"DEBUG: 工作流返回错误字段 - error值: {repr(result['error'])}")
        serialized = serialize_result(result)
        print(f"DEBUG: 序列化后结果键: {list(serialized.keys()) if isinstance(serialized, dict) else 'Not a dict'}")
        if isinstance(serialized, dict) and "error" in serialized:
            print(f"DEBUG: 序列化后错误字段 - error值: {repr(serialized['error'])}")
        return serialized
    except Exception as e:
        return serialize_result({
            "error": str(e),
            "final_illustration": None,
            "processed_story": None,
            "characters": [],
            "storyboards": [],
            "generated_images": [],
            "messages": [{"content": f"插画生成失败: {str(e)}"}]
        })


# 同步版本
def generate_illustration_sync(
    user_input: str,
    style_preference: str = "anime",
    num_panels: int = 4,
    config: RunnableConfig = None,
    progress_callback: callable = None,
    multi_edit_model_id: str = "flux3dc-cff2-4177-ad3a-28d9b4d3ff48"
) -> dict:
    """
    运行插画生成工作流（同步版本）
    
    Args:
        user_input: 用户输入的故事内容
        style_preference: 风格偏好
        num_panels: 分镜数量
        config: 运行配置
        progress_callback: 进度回调函数
        
    Returns:
        生成结果
    """
    # 构建初始状态
    initial_state = {
        "user_input": user_input,
        "style_preference": style_preference,
        "num_panels": num_panels,
        "multi_edit_model_id": multi_edit_model_id,
        "messages": [],
        "story_segments": [],
        "storyboards": [],
        "characters": [],
        "character_base_images": [],
        "scene_prompts": [],
        "generated_images": [],
        "main_character": None,
        "final_illustration": None,
        "processed_story": "",
        "story_type": "",
        "reasoning_model": "qwen-max-0403",
        "progress_callback": progress_callback  # 添加到状态中
    }
    
    # 运行图
    try:
        result = illustration_graph.invoke(initial_state, config=config)
        return serialize_result(result)
    except Exception as e:
        return serialize_result({
            "error": str(e),
            "final_illustration": None,
            "processed_story": None,
            "characters": [],
            "storyboards": [],
            "generated_images": [],
            "messages": [{"content": f"插画生成失败: {str(e)}"}]
        })
