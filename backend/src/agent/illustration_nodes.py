"""插画生成Agent的节点实现"""

import asyncio
from typing import Dict, Any, List
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import AIMessage

# 直接导入，不使用try-except来避免部分导入问题
from agent.state import IllustrationState
from agent.configuration import Configuration
from agent.llm_utils import get_default_llm, create_structured_output_chain
from agent.image_utils import image_api, image_processor
from agent.async_image_manager import async_image_manager


def _determine_optimal_panel_count(user_input: str, configurable) -> int:
    """
    根据故事内容自动判断合理的分镜数量

    Args:
        user_input: 用户输入的故事内容
        configurable: 配置对象

    Returns:
        建议的分镜数量 (1-20)
    """
    if not user_input or len(user_input.strip()) == 0:
        return configurable.default_num_panels

    # 基于文本长度的基础判断
    text_length = len(user_input.strip())

    # 计算句子数量（简单的句号、问号、感叹号计数）
    sentence_count = user_input.count('。') + user_input.count('？') + user_input.count('！') + \
                    user_input.count('.') + user_input.count('?') + user_input.count('!')

    # 计算段落数量（换行符计数）
    paragraph_count = max(1, user_input.count('\n') + 1)

    # 检测关键词来判断故事复杂度
    action_keywords = ['然后', '接着', '突然', '后来', '最后', '首先', '接下来', '同时', 'meanwhile', 'then', 'next', 'finally', 'suddenly']
    action_count = sum(1 for keyword in action_keywords if keyword in user_input.lower())

    # 检测场景转换词
    scene_keywords = ['来到', '走进', '回到', '到达', '进入', '离开', '前往', 'went to', 'arrived at', 'entered', 'left']
    scene_count = sum(1 for keyword in scene_keywords if keyword in user_input.lower())

    # 综合判断分镜数量
    if text_length < 100:
        # 短文本：1-3个分镜
        panels = min(3, max(1, sentence_count))
    elif text_length < 300:
        # 中等文本：2-6个分镜
        panels = min(6, max(2, sentence_count // 2 + action_count))
    elif text_length < 600:
        # 较长文本：4-10个分镜
        panels = min(10, max(4, sentence_count // 3 + action_count + scene_count))
    elif text_length < 1000:
        # 长文本：6-15个分镜
        panels = min(15, max(6, sentence_count // 4 + action_count + scene_count + paragraph_count))
    else:
        # 很长文本：8-20个分镜
        panels = min(20, max(8, sentence_count // 5 + action_count + scene_count + paragraph_count * 2))

    # 确保在有效范围内
    panels = max(1, min(configurable.max_num_panels, panels))

    print(f"自动判断分镜数量: 文本长度={text_length}, 句子数={sentence_count}, 动作词={action_count}, 场景词={scene_count}, 建议分镜={panels}")

    return panels


# 创建同步的图像生成器
class SyncImageGenerator:
    """同步图像生成器（模拟）"""

    @staticmethod
    def generate_character_images(characters, style, progress_callback=None):
        """生成角色图片（模拟）"""
        if progress_callback:
            progress_callback("开始生成角色图片...")

        character_images = {}
        for character in characters:
            character_name = character["name"]
            character_images[character_name] = f"mock://character_{character_name}.jpg"
            if progress_callback:
                progress_callback(f"生成角色 {character_name} 的图片")

        return character_images

    @staticmethod
    def generate_scene_images(scene_prompts, character_images, style, progress_callback=None):
        """生成场景图片（模拟）"""
        if progress_callback:
            progress_callback("开始生成场景图片...")

        generated_images = []
        for prompt_data in scene_prompts:
            panel_id = prompt_data.get("panel_id", "0")
            generated_images.append({
                "panel_id": panel_id,
                "image_url": f"mock://scene_{panel_id}.jpg",
                "prompt_used": prompt_data.get("prompt", ""),
                "generation_type": prompt_data.get("generation_type", "text2img"),
                "success": True,
                "error": ""
            })
            if progress_callback:
                progress_callback(f"生成分镜 {panel_id} 的图片")

        return generated_images

    @staticmethod
    def generate_all_images(characters, scene_prompts, style, progress_callback=None):
        """生成所有图片（模拟）"""
        if progress_callback:
            progress_callback("开始生成所有图片...")

        # 生成角色图片
        character_images = SyncImageGenerator.generate_character_images(
            characters, style, progress_callback
        )

        # 生成场景图片
        scene_images = SyncImageGenerator.generate_scene_images(
            scene_prompts, character_images, style, progress_callback
        )

        return character_images, scene_images

# 创建全局实例
sync_image_generator = SyncImageGenerator()
from agent.tools_and_schemas import (
    StoryProcessingResult,
    StorySegmentationResult,
    CharacterExtractionResult,
    StoryboardResult,
    ScenePromptResult,
    ImageGenerationResult
)


def convert_camera_angle_to_english(camera_angle: str) -> str:
    """
    将中文镜头角度转换为英文摄影术语

    Args:
        camera_angle: 中文镜头角度描述

    Returns:
        英文摄影术语
    """
    angle_mapping = {
        "特写镜头": "close-up shot, detailed facial expression, intimate view",
        "特写": "close-up shot, detailed facial expression",
        "中景镜头": "medium shot, half body view, character interaction",
        "中景": "medium shot, half body view",
        "全景镜头": "wide shot, full scene view, establishing shot",
        "全景": "wide shot, full scene view",
        "俯视镜头": "bird's eye view, top-down perspective, overhead shot",
        "俯视": "bird's eye view, top-down perspective",
        "仰视镜头": "low angle shot, upward perspective, dramatic angle",
        "仰视": "low angle shot, upward perspective",
        "侧面镜头": "profile view, side angle, lateral perspective",
        "侧面": "profile view, side angle",
        "正面视角": "front view, direct angle, frontal perspective",
        "正面": "front view, direct angle"
    }

    # 尝试精确匹配
    if camera_angle in angle_mapping:
        return angle_mapping[camera_angle]

    # 尝试部分匹配
    for chinese_term, english_term in angle_mapping.items():
        if chinese_term in camera_angle:
            return english_term

    # 如果没有匹配，返回原始描述
    return camera_angle


def enhance_prompt_with_camera_angle(prompt: str, camera_angle: str, style: str = "anime") -> str:
    """
    根据镜头角度和风格增强提示词

    Args:
        prompt: 原始提示词
        camera_angle: 镜头角度
        style: 艺术风格

    Returns:
        增强后的提示词
    """
    english_angle = convert_camera_angle_to_english(camera_angle)

    # 根据风格添加特定的质量标签
    style_tags = {
        "anime": "anime style, high quality, detailed illustration, vibrant colors",
        "realistic": "photorealistic, high resolution, detailed, professional photography",
        "cartoon": "cartoon style, colorful, expressive, clean lines",
        "art": "artistic style, creative composition, aesthetic, masterpiece"
    }

    base_style = style_tags.get(style.lower(), style_tags["anime"])

    # 根据镜头类型添加特定的描述
    if "close-up" in english_angle.lower():
        # 特写镜头：强调面部表情和情感细节
        angle_specific = "detailed facial features, expressive eyes, emotional depth, intimate portrait, sharp focus on face"
        enhanced_prompt = f"{english_angle}, {prompt}, {angle_specific}, {base_style}"
    elif "medium shot" in english_angle.lower():
        # 中景镜头：强调角色动作和互动
        angle_specific = "character interaction, body language, dynamic pose, upper body focus, balanced composition"
        enhanced_prompt = f"{english_angle}, {prompt}, {angle_specific}, {base_style}"
    elif "wide shot" in english_angle.lower():
        # 全景镜头：强调环境和场景
        angle_specific = "environmental context, scene composition, atmospheric perspective, full scene view, cinematic framing"
        enhanced_prompt = f"{english_angle}, {prompt}, {angle_specific}, {base_style}"
    elif "bird's eye" in english_angle.lower():
        # 俯视镜头：强调空间布局
        angle_specific = "spatial layout, overhead composition, dramatic perspective, top-down view, architectural composition"
        enhanced_prompt = f"{english_angle}, {prompt}, {angle_specific}, {base_style}"
    elif "low angle" in english_angle.lower():
        # 仰视镜头：强调力量感
        angle_specific = "powerful composition, dramatic lighting, imposing presence, heroic angle, dynamic perspective"
        enhanced_prompt = f"{english_angle}, {prompt}, {angle_specific}, {base_style}"
    elif "profile" in english_angle.lower():
        # 侧面镜头：强调轮廓和优雅
        angle_specific = "elegant profile, side silhouette, graceful pose, artistic composition, clean lines"
        enhanced_prompt = f"{english_angle}, {prompt}, {angle_specific}, {base_style}"
    else:
        # 其他镜头角度
        enhanced_prompt = f"{english_angle}, {prompt}, {base_style}"

    return enhanced_prompt
from agent.illustration_prompts import (
    story_processing_instructions,
    story_segmentation_instructions,
    character_extraction_instructions,
    storyboard_generation_instructions,
    prompt_optimization_instructions,
    get_current_date
)



def input_handler(state: IllustrationState, config: RunnableConfig) -> Dict[str, Any]:
    """
    故事输入处理节点
    
    根据用户输入自动选择总结或扩写故事功能
    
    Args:
        state: 当前状态
        config: 运行配置
        
    Returns:
        更新后的状态
    """
    # 获取进度回调函数
    progress_callback = state.get("progress_callback")
    if progress_callback:
        progress_callback("input_handler", "in_progress")
    
    configurable = Configuration.from_runnable_config(config)
    
    # 获取用户输入
    user_input = state.get("user_input", "")
    if not user_input:
        # 从messages中提取用户输入
        messages = state.get("messages", [])
        if messages:
            user_input = messages[-1].content if hasattr(messages[-1], 'content') else str(messages[-1])
    
    # 获取配置参数
    style_preference = state.get("style_preference", configurable.default_style)
    num_panels = state.get("num_panels", configurable.default_num_panels)

    # 如果num_panels为0，需要自动判断合理的分镜数量
    auto_determine_panels = (num_panels == 0)
    if auto_determine_panels:
        num_panels = _determine_optimal_panel_count(user_input, configurable)
    
    # 初始化LLM
    llm = get_default_llm(
        model_name=configurable.story_processor_model,
        temperature=0.7
    )
    structured_llm = create_structured_output_chain(llm, StoryProcessingResult)
    
    # 格式化提示词
    current_date = get_current_date()
    formatted_prompt = story_processing_instructions.format(
        current_date=current_date,
        user_input=user_input,
        style_preference=style_preference,
        num_panels=num_panels
    )
    
    # 处理故事
    try:
        print(f"----------------分割线-------------------")
        print(f"处理之前: {user_input}")
        print(f"准备调用LLM，模型: {configurable.story_processor_model}")
        
        result = structured_llm.invoke(formatted_prompt)
        print(f"LLM调用成功，结果类型: {type(result)}")
        
        if hasattr(result, 'processed_story'):
            print(f"处理之后: {result.processed_story}")
        else:
            print(f"警告: result对象没有processed_story属性")
            print(f"result属性: {dir(result)}")
        
        print(f"----------------分割线-------------------")
        
        # 标记完成
        if progress_callback:
            progress_callback("input_handler", "completed", {
                "processed_story": result.processed_story,
                "story_type": result.story_type,
                "num_panels": num_panels,
                "auto_determined": auto_determine_panels
            })
        
        return {
            "user_input": user_input,
            "processed_story": result.processed_story,
            "processed_story_en": result.processed_story_en,
            "story_type": result.story_type,
            "style_preference": style_preference,
            "num_panels": num_panels,
            "reasoning_model": configurable.story_processor_model,
            "progress_callback": progress_callback  # 保持进度回调
        }
    except Exception as e:
        # 错误处理
        print(f"❗ 故事处理发生异常: {str(e)}")
        print(f"异常类型: {type(e)}")
        import traceback
        print(f"详细堆栈: {traceback.format_exc()}")

        # 标记错误
        if progress_callback:
            progress_callback("input_handler", "error", {"error": str(e)})

        raise e
        # return {
        #     "user_input": user_input,
        #     "processed_story": user_input,  # 使用原始输入作为备选
        #     "story_type": "original",
        #     "style_preference": style_preference,
        #     "num_panels": num_panels,
        #     "reasoning_model": configurable.story_processor_model,
        #     "messages": [AIMessage(content=f"故事处理出现错误: {str(e)}")]
        # }


def story_splitter(state: IllustrationState, config: RunnableConfig) -> Dict[str, Any]:
    """
    故事分段节点
    
    将故事拆分为多个分段并优化为分镜描述
    
    Args:
        state: 当前状态
        config: 运行配置
        
    Returns:
        更新后的状态
    """
    # 获取进度回调函数
    progress_callback = state.get("progress_callback")
    if progress_callback:
        progress_callback("story_splitter", "in_progress")
    
    configurable = Configuration.from_runnable_config(config)
    
    # 获取处理后的故事
    story_content = state.get("processed_story", "")
    style_preference = state.get("style_preference", configurable.default_style)
    num_panels = state.get("num_panels", configurable.default_num_panels)
    
    # 初始化LLM
    llm = get_default_llm(
        model_name=configurable.storyboard_generator_model,
        temperature=0.7
    )
    structured_llm = create_structured_output_chain(llm, StorySegmentationResult)
    
    # 格式化提示词
    formatted_prompt = story_segmentation_instructions.format(
        story_content=story_content,
        num_segments=num_panels,
        style_preference=style_preference
    )
    
    # 分段故事
    try:
        result = structured_llm.invoke(formatted_prompt)
        
        # 转换为状态格式
        segments = []
        for segment in result.segments:
            segments.append({
                "segment_id": segment.segment_id,
                "content": segment.content,
                "content_en": segment.content_en,
                "key_elements": segment.key_elements
            })
        
        # 标记完成
        if progress_callback:
            progress_callback("story_splitter", "completed", {
                "segments_count": len(segments)
            })
        
        return {
            "story_segments": segments,
            "progress_callback": progress_callback  # 保持进度回调
        }
    except Exception as e:
        # 标记错误
        if progress_callback:
            progress_callback("story_splitter", "error", {"error": str(e)})
        
        # 错误处理 - 简单分段
        segments = []
        words = story_content.split()
        words_per_segment = len(words) // num_panels
        
        # 如果有英文版本，优先使用英文版本进行分段
        story_content_en = state.get("processed_story_en", "")
        words_en = []
        words_per_segment_en = 0
        if story_content_en:
            words_en = story_content_en.split()
            words_per_segment_en = len(words_en) // num_panels
        
        for i in range(num_panels):
            start_idx = i * words_per_segment
            end_idx = (i + 1) * words_per_segment if i < num_panels - 1 else len(words)
            segment_content = " ".join(words[start_idx:end_idx])
            
            # 英文版本分段
            if story_content_en and words_en:
                start_idx_en = i * words_per_segment_en
                end_idx_en = (i + 1) * words_per_segment_en if i < num_panels - 1 else len(words_en)
                segment_content_en = " ".join(words_en[start_idx_en:end_idx_en])
            else:
                segment_content_en = f"English version of segment {i + 1}: {segment_content}"
            
            segments.append({
                "segment_id": i + 1,
                "content": segment_content,
                "content_en": segment_content_en,
                "key_elements": []
            })
        
        return {
            "story_segments": segments,
            "progress_callback": progress_callback  # 保持进度回调
        }


def character_extractor(state: IllustrationState, config: RunnableConfig) -> Dict[str, Any]:
    """
    角色特征提取节点
    
    提取主角和关键角色的外貌特征和风格设定
    
    Args:
        state: 当前状态
        config: 运行配置
        
    Returns:
        更新后的状态
    """
    # 获取进度回调函数
    progress_callback = state.get("progress_callback")
    if progress_callback:
        progress_callback("character_extractor", "in_progress")
    
    configurable = Configuration.from_runnable_config(config)
    
    # 获取故事内容
    story_content = state.get("processed_story", "")
    style_preference = state.get("style_preference", configurable.default_style)
    
    # 初始化LLM
    llm = get_default_llm(
        model_name=configurable.character_extractor_model,
        temperature=0.7
    )
    structured_llm = create_structured_output_chain(llm, CharacterExtractionResult)
    
    # 格式化提示词
    formatted_prompt = character_extraction_instructions.format(
        story_content=story_content,
        style_preference=style_preference
    )
    
    # 提取角色信息
    try:
        result = structured_llm.invoke(formatted_prompt)
        
        # 转换为状态格式
        characters = []
        for char in result.characters:
            characters.append({
                "name": char.name,
                "description": char.description,
                "appearance": char.appearance,  # 英文描述，用于生图
                "appearance_cn": char.appearance_cn,  # 中文描述，用于显示
                "style": char.style,  # 英文描述，用于生图
                "style_cn": char.style_cn,  # 中文描述，用于显示
                "role": char.role,
                "base_image_url": None  # 将在后续节点中生成
            })

        main_character = None
        if result.main_character:
            main_character = {
                "name": result.main_character.name,
                "description": result.main_character.description,
                "appearance": result.main_character.appearance,  # 英文描述，用于生图
                "appearance_cn": result.main_character.appearance_cn,  # 中文描述，用于显示
                "style": result.main_character.style,  # 英文描述，用于生图
                "style_cn": result.main_character.style_cn,  # 中文描述，用于显示
                "role": result.main_character.role,
                "base_image_url": None
            }
        
        # 标记完成
        if progress_callback:
            progress_callback("character_extractor", "completed", {
                "characters_count": len(characters),
                "has_main_character": main_character is not None
            })
        
        return {
            "characters": characters,
            "main_character": main_character,
            "progress_callback": progress_callback  # 保持进度回调
        }
    except Exception as e:
        # 标记错误
        if progress_callback:
            progress_callback("character_extractor", "error", {"error": str(e)})
        
        # 错误处理 - 创建默认角色
        default_character = {
            "name": "主角",
            "description": "故事的主要角色",
            "appearance": f"character design suitable for {style_preference} style, detailed features",
            "appearance_cn": f"适合{style_preference}风格的角色设计",
            "style": f"{style_preference} style clothing and accessories",
            "style_cn": f"{style_preference}风格的服装和配饰",
            "role": "main",
            "base_image_url": None
        }
        
        return {
            "characters": [default_character],
            "main_character": default_character,
            "progress_callback": progress_callback  # 保持进度回调
        }


def character_image_generator(state: IllustrationState, config: RunnableConfig) -> Dict[str, Any]:
    """
    角色基准图生成节点（异步版本）

    使用异步管理器生成角色基准图

    Args:
        state: 当前状态
        config: 运行配置

    Returns:
        更新后的状态
    """
    # 获取进度回调函数
    node_progress_callback = state.get("progress_callback")
    if node_progress_callback:
        node_progress_callback("character_image_generator", "in_progress")
    
    configurable = Configuration.from_runnable_config(config)

    # 获取角色信息
    characters = state.get("characters", [])
    main_character = state.get("main_character")
    style_preference = state.get("style_preference", configurable.default_style)

    character_base_images = []

    try:
        # 准备角色列表
        characters_to_generate = []
        if main_character:
            characters_to_generate.append(main_character)

        # 添加重要配角（最多3个角色）
        for character in characters:
            if character.get("role") == "supporting" and len(characters_to_generate) < 3:
                characters_to_generate.append(character)

        if characters_to_generate:
            # 定义进度回调（用于内部mock生成器）
            def local_progress_callback(message: str):
                print(f"角色基准图生成进度: {message}")

            # 使用同步图像生成器
            character_images = sync_image_generator.generate_character_images(
                characters_to_generate,
                style_preference,
                local_progress_callback
            )

            # 更新角色信息和基准图列表
            for character in characters_to_generate:
                character_name = character["name"]
                if character_name in character_images:
                    character["base_image_url"] = character_images[character_name]
                    character_base_images.append({
                        "character_name": character_name,
                        "image_url": character_images[character_name],
                        "prompt_used": f"{character['appearance']}, {style_preference} style"  # 使用英文描述
                    })

            # 更新主角信息
            if main_character and main_character["name"] in character_images:
                main_character["base_image_url"] = character_images[main_character["name"]]

        # 标记完成
        if node_progress_callback:
            node_progress_callback("character_image_generator", "completed", {
                "character_images_count": len(character_base_images)
            })

    except Exception as e:
        # 标记错误
        if node_progress_callback:
            node_progress_callback("character_image_generator", "error", {"error": str(e)})
        
        print(f"角色基准图生成异常: {str(e)}")
        # 返回错误信息但不中断流程
        return {
            "character_base_images": [],
            "main_character": main_character,
            "characters": characters,
            "progress_callback": node_progress_callback  # 保持进度回调
        }

    return {
        "character_base_images": character_base_images,
        "main_character": main_character,
        "characters": characters,
        "progress_callback": node_progress_callback  # 保持进度回调
    }


def storyboard_generator(state: IllustrationState, config: RunnableConfig) -> Dict[str, Any]:
    """
    分镜生成节点

    将故事分段转换为详细的分镜描述

    Args:
        state: 当前状态
        config: 运行配置

    Returns:
        更新后的状态
    """
    # 获取进度回调函数
    progress_callback = state.get("progress_callback")
    if progress_callback:
        progress_callback("storyboard_generator", "in_progress")
    
    configurable = Configuration.from_runnable_config(config)

    # 获取故事分段和角色信息
    story_segments = state.get("story_segments", [])
    characters = state.get("characters", [])
    style_preference = state.get("style_preference", configurable.default_style)

    # 准备角色信息字符串
    characters_info = "\n".join([
        f"- {char['name']}: {char['description']} | 外貌: {char['appearance']}"
        for char in characters
    ])

    # 准备故事分段字符串（优先使用英文版本）
    segments_text = "\n".join([
        f"分段{seg['segment_id']}: {seg.get('content_en', seg['content'])}"
        for seg in story_segments
    ])

    # 初始化LLM
    llm = get_default_llm(
        model_name=configurable.storyboard_generator_model,
        temperature=0.7
    )
    structured_llm = create_structured_output_chain(llm, StoryboardResult)

    # 格式化提示词
    formatted_prompt = storyboard_generation_instructions.format(
        story_segments=segments_text,
        characters_info=characters_info,
        style_preference=style_preference
    )

    # 生成分镜
    try:
        result = structured_llm.invoke(formatted_prompt)

        # 转换为状态格式
        storyboards = []
        for panel in result.panels:
            storyboards.append({
                "panel_id": panel.panel_id,
                "scene_description": panel.scene_description,
                "characters_involved": panel.characters_involved,
                "environment": panel.environment,
                "action": panel.action,
                "mood": panel.mood,
                "camera_angle": panel.camera_angle
            })

        # 标记完成
        if progress_callback:
            progress_callback("storyboard_generator", "completed", {
                "storyboards_count": len(storyboards)
            })
        
        return {
            "storyboards": storyboards,
            "progress_callback": progress_callback  # 保持进度回调
        }

    except Exception as e:
        # 标记错误
        if progress_callback:
            progress_callback("storyboard_generator", "error", {"error": str(e)})
        
        # 错误处理 - 基于故事分段创建简单分镜
        storyboards = []
        for i, segment in enumerate(story_segments):
            # 优先使用英文版本内容
            scene_content = segment.get('content_en', segment['content'])
            storyboards.append({
                "panel_id": str(i + 1),
                "scene_description": scene_content,
                "characters_involved": [char["name"] for char in characters if char["role"] == "main"],
                "environment": "scene environment",
                "action": "character action",
                "mood": "story atmosphere",
                "camera_angle": "front view"
            })

        return {
            "storyboards": storyboards,
            "progress_callback": progress_callback  # 保持进度回调
        }


def scene_prompt_optimizer(state: IllustrationState, config: RunnableConfig) -> Dict[str, Any]:
    """
    分镜提示词优化节点

    将分镜转换为第三人称客观描述的提示词

    Args:
        state: 当前状态
        config: 运行配置

    Returns:
        更新后的状态
    """
    # 获取进度回调函数
    progress_callback = state.get("progress_callback")
    if progress_callback:
        progress_callback("scene_prompt_optimizer", "in_progress")
    
    configurable = Configuration.from_runnable_config(config)

    # 获取分镜和角色信息
    storyboards = state.get("storyboards", [])
    characters = state.get("characters", [])
    main_character = state.get("main_character")
    style_preference = state.get("style_preference", configurable.default_style)

    # 准备角色信息字符串（使用英文描述用于生图）
    characters_info = "\n".join([
        f"- {char['name']}: {char['description']} | Appearance: {char['appearance']} | Style: {char['style']}"
        for char in characters
    ])

    # 为每个分镜添加涉及角色的详细信息
    enhanced_storyboards = []
    for panel in storyboards:
        enhanced_panel = panel.copy()
        # 收集涉及角色的英文描述
        involved_chars_details = []
        for char_name in panel.get('characters_involved', []):
            for char in characters:
                if char['name'] == char_name:
                    involved_chars_details.append({
                        'name': char['name'],
                        'appearance': char['appearance'],  # 英文描述
                        'style': char['style']  # 英文描述
                    })
                    break
        enhanced_panel['characters_details'] = involved_chars_details
        enhanced_storyboards.append(enhanced_panel)

    # 准备分镜信息字符串（包含角色详细信息）
    storyboard_text = "\n".join([
        f"Panel {panel['panel_id']}: Scene-{panel['scene_description']} | Characters-{', '.join([char['name'] + ' (' + char['appearance'] + ', ' + char['style'] + ')' for char in panel.get('characters_details', [])])} | Environment-{panel['environment']} | Action-{panel['action']} | Mood-{panel['mood']} | Camera-{panel['camera_angle']}"
        for panel in enhanced_storyboards
    ])

    # 初始化LLM
    llm = get_default_llm(
        model_name=configurable.prompt_optimizer_model,
        temperature=0.5  # 降低温度以获得更一致的提示词
    )
    structured_llm = create_structured_output_chain(llm, ScenePromptResult)

    # 格式化提示词
    formatted_prompt = prompt_optimization_instructions.format(
        storyboard_panels=storyboard_text,
        characters_info=characters_info,
        style_preference=style_preference
    )

    # 优化提示词
    try:
        result = structured_llm.invoke(formatted_prompt)

        # 转换为状态格式
        scene_prompts = []
        
        # 处理不同的返回格式
        if hasattr(result, 'prompts'):
            # 正常的ScenePromptResult格式
            for prompt_data in result.prompts:
                # 找到对应的分镜信息以获取镜头角度
                camera_angle = ""
                for panel in enhanced_storyboards:
                    if panel.get('panel_id') == prompt_data.panel_id:
                        camera_angle = panel.get('camera_angle', '')
                        break

                # 使用镜头角度增强提示词
                enhanced_prompt = enhance_prompt_with_camera_angle(
                    prompt_data.prompt,
                    camera_angle,
                    style_preference
                )

                scene_prompts.append({
                    "panel_id": prompt_data.panel_id,
                    "prompt": enhanced_prompt,
                    "style_tags": prompt_data.style_tags,
                    "generation_type": prompt_data.generation_type,
                    "characters_involved": getattr(prompt_data, 'characters_involved', []),
                    "camera_angle": camera_angle
                })
        elif isinstance(result, list):
            # 如果LLM直接返回了列表格式
            for i, prompt_data in enumerate(result):
                if isinstance(prompt_data, dict):
                    panel_id = prompt_data.get("panel_id", str(i + 1))
                    original_prompt = prompt_data.get("prompt", "")

                    # 找到对应的分镜信息以获取镜头角度
                    camera_angle = ""
                    for panel in enhanced_storyboards:
                        if panel.get('panel_id') == panel_id:
                            camera_angle = panel.get('camera_angle', '')
                            break

                    # 使用镜头角度增强提示词
                    enhanced_prompt = enhance_prompt_with_camera_angle(original_prompt, camera_angle, style_preference)

                    scene_prompts.append({
                        "panel_id": panel_id,
                        "prompt": enhanced_prompt,
                        "style_tags": prompt_data.get("style_tags", [style_preference]),
                        "generation_type": prompt_data.get("generation_type", "text2img"),
                        "characters_involved": prompt_data.get("characters_involved", []),
                        "camera_angle": camera_angle
                    })
                else:
                    # 如果列表中的元素不是字典，尝试转换
                    panel_id = getattr(prompt_data, 'panel_id', str(i + 1))
                    original_prompt = getattr(prompt_data, 'prompt', '')

                    # 找到对应的分镜信息以获取镜头角度
                    camera_angle = ""
                    for panel in enhanced_storyboards:
                        if panel.get('panel_id') == panel_id:
                            camera_angle = panel.get('camera_angle', '')
                            break

                    # 使用镜头角度增强提示词
                    enhanced_prompt = enhance_prompt_with_camera_angle(original_prompt, camera_angle, style_preference)

                    scene_prompts.append({
                        "panel_id": panel_id,
                        "prompt": enhanced_prompt,
                        "style_tags": getattr(prompt_data, 'style_tags', [style_preference]),
                        "generation_type": getattr(prompt_data, 'generation_type', 'text2img'),
                        "characters_involved": getattr(prompt_data, 'characters_involved', []),
                        "camera_angle": camera_angle
                    })
        else:
            # 如果都不符合，尝试直接使用result的prompts属性
            prompts_data = getattr(result, 'prompts', [])
            for prompt_data in prompts_data:
                # 找到对应的分镜信息以获取镜头角度
                camera_angle = ""
                for panel in enhanced_storyboards:
                    if panel.get('panel_id') == prompt_data.panel_id:
                        camera_angle = panel.get('camera_angle', '')
                        break

                # 使用镜头角度增强提示词
                enhanced_prompt = enhance_prompt_with_camera_angle(
                    prompt_data.prompt,
                    camera_angle,
                    style_preference
                )

                scene_prompts.append({
                    "panel_id": prompt_data.panel_id,
                    "prompt": enhanced_prompt,
                    "style_tags": prompt_data.style_tags,
                    "generation_type": prompt_data.generation_type,
                    "characters_involved": getattr(prompt_data, 'characters_involved', []),
                    "camera_angle": camera_angle
                })

        # 标记完成
        if progress_callback:
            progress_callback("scene_prompt_optimizer", "completed", {
                "prompts_count": len(scene_prompts)
            })
        
        return {
            "scene_prompts": scene_prompts,
            "progress_callback": progress_callback  # 保持进度回调
        }

    except Exception as e:
        # 标记错误
        if progress_callback:
            progress_callback("scene_prompt_optimizer", "error", {"error": str(e)})

        # 提示词优化出错时中断后续流程
        print(f"提示词优化失败，中断后续流程: {str(e)}")
        raise Exception(f"提示词优化失败: {str(e)}")


def image_generator(state: IllustrationState, config: RunnableConfig) -> Dict[str, Any]:
    """
    图像生成节点（异步版本）

    使用异步管理器生成分镜插画

    Args:
        state: 当前状态
        config: 运行配置

    Returns:
        更新后的状态
    """
    # 获取进度回调函数
    node_progress_callback = state.get("progress_callback")
    if node_progress_callback:
        node_progress_callback("image_generator", "in_progress")
    
    configurable = Configuration.from_runnable_config(config)

    # 获取场景提示词和角色信息
    scene_prompts = state.get("scene_prompts", [])
    characters = state.get("characters", [])
    character_base_images = state.get("character_base_images", [])
    style_preference = state.get("style_preference", configurable.default_style)

    generated_images = []

    try:
        if scene_prompts:
            # 构建角色图片映射
            character_images = {}
            for img_info in character_base_images:
                character_images[img_info["character_name"]] = img_info["image_url"]

            # 定义进度回调（用于内部mock生成器）
            def local_progress_callback(message: str):
                print(f"场景图片生成进度: {message}")

            # 使用同步图像生成器
            generated_images = sync_image_generator.generate_scene_images(
                scene_prompts,
                character_images,
                style_preference,
                local_progress_callback
            )

    except Exception as e:
        # 标记错误
        if node_progress_callback:
            node_progress_callback("image_generator", "error", {"error": str(e)})
        
        print(f"图像生成异常: {str(e)}")
        # 创建失败的结果
        generated_images = []
        for prompt_data in scene_prompts:
            generated_images.append({
                "panel_id": prompt_data.get("panel_id", 0),
                "image_url": "",
                "prompt_used": prompt_data.get("prompt", ""),
                "generation_type": prompt_data.get("generation_type", "text2img"),
                "success": False,
                "error": str(e)
            })

    # 标记完成
    if node_progress_callback:
        node_progress_callback("image_generator", "completed", {
            "generated_images_count": len(generated_images),
            "successful_images": len([img for img in generated_images if img.get("success")])
        })

    return {
        "generated_images": generated_images,
        "progress_callback": node_progress_callback  # 保持进度回调
    }


def unified_image_generator(state: IllustrationState, config: RunnableConfig) -> Dict[str, Any]:
    """
    统一图像生成节点

    同时生成角色基准图和场景图片，提供统一的进度管理

    Args:
        state: 当前状态
        config: 运行配置

    Returns:
        更新后的状态
    """
    # 获取进度回调函数
    node_progress_callback = state.get("progress_callback")
    if node_progress_callback:
        node_progress_callback("unified_image_generator", "in_progress")
    
    configurable = Configuration.from_runnable_config(config)

    # 获取输入数据
    characters = state.get("characters", [])
    scene_prompts = state.get("scene_prompts", [])
    storyboards = state.get("storyboards", [])
    style_preference = state.get("style_preference", configurable.default_style)
    multi_edit_model_id = state.get("multi_edit_model_id", "flux3dc-cff2-4177-ad3a-28d9b4d3ff48")

    character_base_images = []
    generated_images = []
    storyboard_images = []

    try:
        # 定义统一的进度回调（用于内部异步任务）
        def local_progress_callback(message: str):
            print(f"图像生成进度: {message}")

        # 使用异步图像管理器进行真实的图像生成
        import asyncio
        
        async def generate_all_async():
            """异步生成所有图片（角色图 + 场景图 + 分镜图）"""
            # 1. 先生成角色基准图
            character_images_dict = await async_image_manager.generate_character_images(
                characters,
                style_preference,
                local_progress_callback
            )

            # 2. 使用角色基准图生成场景图片（多图生图）
            scene_images_list = await async_image_manager.generate_scene_with_characters(
                scene_prompts,
                character_images_dict,
                style_preference,
                local_progress_callback,
                multi_edit_model_id
            )

            # 3. 使用角色图和场景图生成分镜图像（多图生图）
            storyboard_images_list = []
            if scene_prompts:
                storyboard_images_list = await async_image_manager.generate_storyboard_images(
                    scene_prompts,
                    character_images_dict,
                    scene_images_list,
                    style_preference,
                    local_progress_callback,
                    multi_edit_model_id
                )

            return character_images_dict, scene_images_list, storyboard_images_list
        
        # 运行异步任务
        try:
            # 尝试获取当前事件循环
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果循环正在运行，创建新任务
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, generate_all_async())
                    character_images_dict, scene_images_list, storyboard_images_list = future.result(timeout=300)  # 5分钟超时
            else:
                # 如果循环未运行，直接运行
                character_images_dict, scene_images_list, storyboard_images_list = asyncio.run(generate_all_async())
        except RuntimeError:
            # 如果没有事件循环，创建新的
            character_images_dict, scene_images_list, storyboard_images_list = asyncio.run(generate_all_async())
        
        # 整理角色基准图结果
        for character in characters:
            character_name = character["name"]
            if character_name in character_images_dict:
                character["base_image_url"] = character_images_dict[character_name]
                character_base_images.append({
                    "character_name": character_name,
                    "image_url": character_images_dict[character_name],
                    "prompt_used": f"{character['appearance']}, {style_preference} style"
                })

        # 更新主角信息
        main_character = state.get("main_character")
        if main_character and main_character["name"] in character_images_dict:
            main_character["base_image_url"] = character_images_dict[main_character["name"]]

        # 场景图片结果
        generated_images = scene_images_list

        # 分镜图片结果
        storyboard_images = storyboard_images_list

        # 标记完成
        if node_progress_callback:
            node_progress_callback("unified_image_generator", "completed", {
                "character_images_count": len(character_base_images),
                "scene_images_count": len(generated_images),
                "storyboard_images_count": len(storyboard_images),
                "successful_scene_images": len([img for img in generated_images if img.get("success")]),
                "successful_storyboard_images": len([img for img in storyboard_images if img.get("image_url")])
            })

        return {
            "character_base_images": character_base_images,
            "generated_images": generated_images + storyboard_images,  # 合并场景图和分镜图
            "main_character": main_character,
            "characters": characters,
            "progress_callback": node_progress_callback  # 保持进度回调
        }

    except Exception as e:
        # 标记错误
        if node_progress_callback:
            node_progress_callback("unified_image_generator", "error", {"error": str(e)})
        
        print(f"统一图像生成异常，回退到mock模式: {str(e)}")

        # 回退到mock模式
        character_images, scene_images = sync_image_generator.generate_all_images(
            characters,
            scene_prompts,
            style_preference,
            lambda msg: print(f"Mock模式: {msg}")
        )

        # 整理角色基准图结果
        for character in characters:
            character_name = character["name"]
            if character_name in character_images:
                character["base_image_url"] = character_images[character_name]
                character_base_images.append({
                    "character_name": character_name,
                    "image_url": character_images[character_name],
                    "prompt_used": f"{character['appearance']}, {style_preference} style"
                })

        # 更新主角信息
        main_character = state.get("main_character")
        if main_character and main_character["name"] in character_images:
            main_character["base_image_url"] = character_images[main_character["name"]]

        # 场景图片结果
        generated_images = scene_images

        return {
            "character_base_images": character_base_images,
            "generated_images": generated_images,
            "main_character": main_character,
            "characters": characters,
            "progress_callback": node_progress_callback  # 保持进度回调
        }


def image_merger(state: IllustrationState, config: RunnableConfig) -> Dict[str, Any]:
    """
    图像合并节点

    将多张分镜图合成为插画序列或拼接图

    Args:
        state: 当前状态
        config: 运行配置

    Returns:
        更新后的状态
    """
    # 获取进度回调函数
    progress_callback = state.get("progress_callback")
    if progress_callback:
        progress_callback("image_merger", "in_progress")
    
    configurable = Configuration.from_runnable_config(config)

    # 获取生成的图片
    generated_images = state.get("generated_images", [])
    num_panels = state.get("num_panels", configurable.default_num_panels)

    # 过滤成功生成的图片
    successful_images = [
        img for img in generated_images
        if img["success"] and img["image_url"]
    ]

    if not successful_images:
        if progress_callback:
            progress_callback("image_merger", "completed", {
                "success": False,
                "message": "没有成功生成的图片可供合并"
            })
        return {
            "final_illustration": None,
            "progress_callback": progress_callback  # 保持进度回调
        }

    try:
        # 按panel_id排序（转换为数字进行排序）
        successful_images.sort(key=lambda x: int(x["panel_id"]) if x["panel_id"].isdigit() else 0)

        # 提取图片URL
        image_urls = [img["image_url"] for img in successful_images]

        # 确定网格布局
        if num_panels <= 2:
            grid_size = (1, num_panels)  # 横向排列
        elif num_panels <= 4:
            grid_size = (2, 2)  # 2x2网格
        elif num_panels <= 6:
            grid_size = (2, 3)  # 2x3网格
        else:
            grid_size = (3, 3)  # 3x3网格

        # 合并图片
        merged_image = image_processor.merge_images_grid(
            image_urls=image_urls,
            grid_size=grid_size,
            output_size=(1024, 1024)
        )

        if merged_image:
            if progress_callback:
                progress_callback("image_merger", "completed", {
                    "success": True,
                    "merged_images_count": len(successful_images),
                    "final_illustration": merged_image is not None
                })
            return {
                "final_illustration": merged_image,
                "progress_callback": progress_callback  # 保持进度回调
            }
        else:
            if progress_callback:
                progress_callback("image_merger", "completed", {
                    "success": False,
                    "message": "图片合并失败"
                })
            return {
                "final_illustration": None,
                "progress_callback": progress_callback  # 保持进度回调
            }

    except Exception as e:
        if progress_callback:
            progress_callback("image_merger", "error", {
                "error": str(e)
            })
        return {
            "final_illustration": None,
            "progress_callback": progress_callback  # 保持进度回调
        }


def finalize_illustration(state: IllustrationState, config: RunnableConfig) -> Dict[str, Any]:
    """
    最终化插画结果节点

    整理和输出最终的插画生成结果

    Args:
        state: 当前状态
        config: 运行配置

    Returns:
        更新后的状态
    """
    # 获取进度回调函数
    progress_callback = state.get("progress_callback")
    if progress_callback:
        progress_callback("finalize_illustration", "in_progress")
    
    # 获取所有结果
    final_illustration = state.get("final_illustration")
    generated_images = state.get("generated_images", [])
    characters = state.get("characters", [])
    storyboards = state.get("storyboards", [])
    processed_story = state.get("processed_story", "")

    # 统计生成结果
    successful_count = len([img for img in generated_images if img["success"]])
    total_count = len(generated_images)

    # 构建结果消息
    result_message = f"""
插画生成完成！

📖 故事处理: {processed_story[:100]}...
👥 角色数量: {len(characters)}
🎬 分镜数量: {len(storyboards)}
🖼️ 图片生成: {successful_count}/{total_count} 成功
🎨 最终插画: {'✅ 已生成' if final_illustration else '❌ 生成失败'}

详细结果:
"""

    # 添加每个分镜的生成结果
    for img in generated_images:
        status = "✅" if img["success"] else "❌"
        result_message += f"\n分镜{img['panel_id']}: {status} {img.get('error', '成功')}"

    # 标记完成
    if progress_callback:
        progress_callback("finalize_illustration", "completed", {
            "final_illustration": final_illustration is not None,
            "successful_images": successful_count,
            "total_images": total_count
        })

    return {
        "final_result_message": result_message,
        "progress_callback": progress_callback  # 保持进度回调
    }
