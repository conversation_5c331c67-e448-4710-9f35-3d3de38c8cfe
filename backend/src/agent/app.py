# mypy: disable - error - code = "no-untyped-def,misc"
import pathlib
import asyncio
import json
import uuid
import time
from typing import Optional, Dict, Any
from fastapi import FastAPI, Response, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from threading import Lock

from agent.illustration_graph import generate_illustration_sync, generate_illustration
from agent.progress_manager import ProgressManager
from agent.async_image_manager import async_image_manager


# 移除了模拟工作流函数，直接使用真实工作流

# 创建全局进度管理器
progress_manager = ProgressManager()

# Define the FastAPI app
app = FastAPI(
    title="插画生成Agent API",
    description="基于LangGraph的智能插画生成系统",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 请求模型
class IllustrationRequest(BaseModel):
    """插画生成请求模型"""
    user_input: str = Field(..., description="用户输入的故事内容")
    style_preference: str = Field(default="anime", description="风格偏好")
    num_panels: int = Field(default=0, description="分镜数量，0表示自动判断，1-20为手动设置", ge=0, le=20)
    multi_edit_model_id: str = Field(default="flux3dc-cff2-4177-ad3a-28d9b4d3ff48", description="多图生图模型ID，支持flux和gemini25")
    
class GenerationStartResponse(BaseModel):
    """生成开始响应模型"""
    success: bool
    session_id: str
    message: str
    error: Optional[str] = None


class IllustrationResponse(BaseModel):
    """插画生成响应模型"""
    success: bool
    message: str
    error: Optional[str] = None
    data: Optional[dict] = None


# API端点
@app.post("/api/start-generation", response_model=GenerationStartResponse)
async def start_generation_endpoint(request: IllustrationRequest, background_tasks: BackgroundTasks):
    """
    开始插画生成的API端点，返回session_id用于监听进度

    Args:
        request: 插画生成请求
        background_tasks: 背景任务管理器

    Returns:
        生成开始响应，包含session_id
    """
    try:
        # 生成唯一会话ID
        session_id = str(uuid.uuid4())
        
        # 初始化进度
        progress_manager.start_session(session_id)
        
        # 在背景任务中运行插画生成
        background_tasks.add_task(
            run_illustration_generation_with_progress,
            session_id,
            request.user_input,
            request.style_preference,
            request.num_panels,
            request.multi_edit_model_id
        )
        
        return GenerationStartResponse(
            success=True,
            session_id=session_id,
            message="插画生成已开始"
        )
    except Exception as e:
        return GenerationStartResponse(
            success=False,
            session_id="",
            message="启动失败",
            error=str(e)
        )


@app.get("/api/progress/{session_id}")
async def progress_stream_endpoint(session_id: str):
    """
    进度流端点，使用Server-Sent Events推送实时进度

    Args:
        session_id: 会话ID

    Returns:
        SSE流响应
    """
    if not progress_manager.session_exists(session_id):
        raise HTTPException(status_code=404, detail="会话ID不存在")
    
    async def event_stream():
        try:
            # 首先发送所有历史进度（供重连后恢复状态）
            all_progress = progress_manager.get_all_progress_updates(session_id)
            print(f"DEBUG: 发送历史进度: {len(all_progress)} 个更新")
            for update in all_progress:
                yield f"data: {json.dumps(update, ensure_ascii=False)}\n\n"
            
            while True:
                # 获取新的进度更新
                progress_data = progress_manager.get_progress_updates(session_id)
                
                if progress_data:
                    print(f"DEBUG: 发送新进度更新: {len(progress_data)} 个")
                    for update in progress_data:
                        yield f"data: {json.dumps(update, ensure_ascii=False)}\n\n"
                
                # 检查是否完成
                if progress_manager.is_completed(session_id):
                    # 发送最终结果
                    final_result = progress_manager.get_final_result(session_id)
                    if final_result:
                        print(f"DEBUG: 发送最终结果")
                        yield f"data: {json.dumps(final_result, ensure_ascii=False)}\n\n"
                    break
                    
                # 检查是否出错
                if progress_manager.has_error(session_id):
                    error_data = progress_manager.get_error(session_id)
                    if error_data:
                        print(f"DEBUG: 发送错误信息")
                        yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
                    break
                
                await asyncio.sleep(0.5)  # 等待500ms再检查
                
        except Exception as e:
            error_event = {
                "type": "error",
                "message": f"进度流错误: {str(e)}"
            }
            yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"
        finally:
            # 清理会话
            progress_manager.cleanup_session(session_id)
    
    return StreamingResponse(
        event_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )


@app.get("/api/concurrent-status")
async def get_concurrent_status():
    """
    获取当前并发任务状态

    Returns:
        并发任务状态信息
    """
    try:
        concurrent_info = async_image_manager.get_concurrent_task_info()
        task_summary = async_image_manager.get_task_status_summary()

        return {
            "success": True,
            "data": {
                "concurrent_info": concurrent_info,
                "task_summary": task_summary,
                "timestamp": time.time()
            }
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "data": None
        }


async def run_illustration_generation_with_progress(
    session_id: str,
    user_input: str,
    style_preference: str,
    num_panels: int,
    multi_edit_model_id: str = "flux3dc-cff2-4177-ad3a-28d9b4d3ff48"
):
    """
    带进度推送的插画生成函数
    
    Args:
        session_id: 会话ID
        user_input: 用户输入
        style_preference: 风格偏好
        num_panels: 分镜数量
    """
    try:
        print(f"开始生成任务, session_id: {session_id}")

        # 创建进度回调函数
        def progress_callback(node_name: str, status: str, data: Any = None):
            progress_manager.update_progress(session_id, node_name, status, data)

        # 设置进度回调
        progress_manager.set_progress_callback(session_id, progress_callback)

        # 调用插画生成工作流
        result = await generate_illustration(
            user_input=user_input,
            style_preference=style_preference,
            num_panels=num_panels,
            config=None,
            progress_callback=progress_callback,
            multi_edit_model_id=multi_edit_model_id
        )

        # 检查是否有真正的错误
        if "error" in result:
            error_value = result['error']
            print(f"DEBUG: 发现错误字段 - error值: {repr(error_value)}, 类型: {type(error_value)}")
            print(f"DEBUG: 完整result键: {list(result.keys())}")

            # 只有当error是有意义的错误信息时才抛出异常
            # 忽略空字符串、'success'、'成功'等非错误值
            if error_value and error_value not in ['success', '成功', '']:
                raise Exception(f"工作流错误: {error_value}")
            else:
                print(f"DEBUG: 忽略非错误的error字段值: {repr(error_value)}")
                # 清理错误字段，避免后续处理问题
                result = result.copy()
                del result['error']

        # 设置最终结果
        progress_manager.set_final_result(session_id, result)
        print(f"生成任务完成, session_id: {session_id}")

    except Exception as e:
        print(f"生成过程出错: {str(e)}")
        progress_manager.set_error(session_id, str(e))
        raise  # 重新抛出异常，不再隐藏错误


@app.post("/api/generate-illustration", response_model=IllustrationResponse)
async def generate_illustration_endpoint(request: IllustrationRequest):
    """
    生成插画的API端点（兼容性版本，用于调试）

    Args:
        request: 插画生成请求

    Returns:
        插画生成结果
    """
    try:
        print(f"收到插画生成请求: {request.user_input[:50]}...")

        # 模拟响应用于测试连接（包含角色图片）
        mock_data = {
            "final_illustration": "https://picsum.photos/800/600?random=1",
            "processed_story": request.user_input,
            "characters": [
                {
                    "name": "小白猫",
                    "description": "一只活泼可爱的白色小猫，充满好奇心",
                    "appearance": "雪白的毛发，蓝色的眼睛，小巧的身形",
                    "role": "main",
                    "base_image_url": "https://picsum.photos/300/400?random=2"
                },
                {
                    "name": "蝴蝶",
                    "description": "花园中翩翩起舞的彩色蝴蝶",
                    "appearance": "五彩斑斓的翅膀，轻盈的身姿",
                    "role": "supporting",
                    "base_image_url": "https://picsum.photos/300/400?random=3"
                }
            ],
            "storyboards": [{"panel_id": 1, "scene_description": "第一个分镜", "action": "开场"}],
            "generated_images": [{"panel_id": 1, "success": True, "image_url": "https://picsum.photos/600/800?random=4"}]
        }

        return IllustrationResponse(
            success=True,
            message="插画生成完成（模拟模式）",
            data=mock_data
        )

    except Exception as e:
        print(f"插画生成异常: {str(e)}")
        return IllustrationResponse(
            success=False,
            message="服务器内部错误",
            error=str(e)
        )


@app.get("/api/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": "illustration-generation-agent"}


@app.post("/api/test-simple")
async def test_simple():
    """简单测试端点"""
    return {
        "success": True,
        "final_illustration": None,
        "processed_story": "测试故事",
        "characters": [],
        "storyboards": [],
        "generated_images": [],
        "message": "测试成功",
        "error": None
    }


@app.post("/api/test-debug")
async def test_debug(request: IllustrationRequest):
    """调试测试端点"""
    try:
        print(f"DEBUG: 收到请求: {request}")

        # 直接调用函数
        from agent.illustration_graph import generate_illustration_sync
        result = generate_illustration_sync(
            user_input=request.user_input,
            style_preference=request.style_preference,
            num_panels=request.num_panels
        )

        print(f"DEBUG: 函数返回结果类型: {type(result)}")
        print(f"DEBUG: 函数返回结果键: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")

        return {
            "success": True,
            "message": "调试测试成功",
            "data": {
                "result_type": str(type(result)),
                "result_keys": list(result.keys()) if isinstance(result, dict) else None,
                "has_error": "error" in result if isinstance(result, dict) else False
            }
        }

    except Exception as e:
        print(f"DEBUG: 异常: {str(e)}")
        import traceback
        traceback.print_exc()

        return {
            "success": False,
            "message": "调试测试失败",
            "error": str(e)
        }


@app.get("/api/styles")
async def get_available_styles():
    """获取可用的艺术风格列表（基于实际生图API能力）"""
    return {
        "styles": [
            {
                "id": "anime",
                "name": "动漫风格",
                "description": "日式动漫插画风格，适合二次元角色和场景",
                "model_type": "anime",
                "recommended": True
            },
            {
                "id": "realistic",
                "name": "写实风格",
                "description": "真实感强的插画风格，适合现实主义场景",
                "model_type": "realistic",
                "recommended": False
            },
            {
                "id": "art",
                "name": "艺术风格",
                "description": "艺术化的插画风格，具有独特的美学表现",
                "model_type": "art",
                "recommended": False
            }
        ],
        "default_style": "anime",
        "note": "风格选择将影响图像生成的模型和效果"
    }


def create_frontend_router(build_dir="../frontend/dist"):
    """Creates a router to serve the React frontend.

    Args:
        build_dir: Path to the React build directory relative to this file.

    Returns:
        A Starlette application serving the frontend.
    """
    build_path = pathlib.Path(__file__).parent.parent.parent / build_dir

    if not build_path.is_dir() or not (build_path / "index.html").is_file():
        print(
            f"WARN: Frontend build directory not found or incomplete at {build_path}. Serving frontend will likely fail."
        )
        # Return a dummy router if build isn't ready
        from starlette.routing import Route

        async def dummy_frontend(request):
            return Response(
                "Frontend not built. Run 'npm run build' in the frontend directory.",
                media_type="text/plain",
                status_code=503,
            )

        return Route("/{path:path}", endpoint=dummy_frontend)

    return StaticFiles(directory=build_path, html=True)


# Mount the frontend under /app to not conflict with the LangGraph API routes
app.mount(
    "/app",
    create_frontend_router(),
    name="frontend",
)
