import os
from pydantic import BaseModel, <PERSON>
from typing import Any, Optional

from langchain_core.runnables import RunnableConfig


class Configuration(BaseModel):
    """The configuration for the agent."""

    # 插画生成Agent配置
    story_processor_model: str = Field(
        default="qwen-max-0403",
        metadata={
            "description": "The name of the language model to use for story processing."
        },
    )

    character_extractor_model: str = Field(
        default="qwen-max-0403",
        metadata={
            "description": "The name of the language model to use for character extraction."
        },
    )

    storyboard_generator_model: str = Field(
        default="qwen-max-0403",
        metadata={
            "description": "The name of the language model to use for storyboard generation."
        },
    )

    prompt_optimizer_model: str = Field(
        default="qwen-max-0403",
        metadata={
            "description": "The name of the language model to use for prompt optimization."
        },
    )

    # 图像生成配置
    image_generation_api_url: str = Field(
        default="http://localhost:8000/generate",
        metadata={
            "description": "The URL for the image generation API."
        },
    )

    image_generation_api_key: str = Field(
        default="",
        metadata={
            "description": "The API key for the image generation service."
        },
    )

    default_style: str = Field(
        default="anime",
        metadata={
            "description": "Default art style for image generation."
        },
    )

    default_num_panels: int = Field(
        default=4,
        metadata={"description": "Default number of panels for storyboard (1-20)."},
    )

    max_num_panels: int = Field(
        default=20,
        metadata={"description": "Maximum number of panels allowed."},
    )

    # 原有搜索Agent配置（保留兼容性）
    query_generator_model: str = Field(
        default="qwen-max-0403",
        metadata={
            "description": "The name of the language model to use for the agent's query generation."
        },
    )

    reflection_model: str = Field(
        default="qwen-max-0403",
        metadata={
            "description": "The name of the language model to use for the agent's reflection."
        },
    )

    answer_model: str = Field(
        default="qwen-max-0403",
        metadata={
            "description": "The name of the language model to use for the agent's answer."
        },
    )

    number_of_initial_queries: int = Field(
        default=3,
        metadata={"description": "The number of initial search queries to generate."},
    )

    max_research_loops: int = Field(
        default=2,
        metadata={"description": "The maximum number of research loops to perform."},
    )

    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "Configuration":
        """Create a Configuration instance from a RunnableConfig."""
        configurable = (
            config["configurable"] if config and "configurable" in config else {}
        )

        # Get raw values from environment or config
        raw_values: dict[str, Any] = {
            name: os.environ.get(name.upper(), configurable.get(name))
            for name in cls.model_fields.keys()
        }

        # Filter out None values
        values = {k: v for k, v in raw_values.items() if v is not None}

        return cls(**values)
