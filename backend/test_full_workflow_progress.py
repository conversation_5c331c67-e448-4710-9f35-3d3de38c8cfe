#!/usr/bin/env python3
"""
测试完整工作流的进度回调
"""
import asyncio
import sys
import os
import time

# 添加src目录到路径
sys.path.append('src')

async def test_full_workflow_progress():
    """测试完整工作流的进度回调"""
    try:
        from agent.illustration_graph import generate_illustration
        
        print('🔄 开始测试完整工作流进度更新...')
        
        # 收集进度更新
        progress_updates = []
        
        def progress_callback(node_name, status, data=None):
            timestamp = time.time()
            update = f'{node_name} -> {status}'
            print(f'📊 进度: {update}')
            progress_updates.append((node_name, status, data, timestamp))
        
        # 执行工作流
        start_time = time.time()
        result = await generate_illustration(
            user_input='小猫在花园里玩耍',
            style_preference='anime',
            num_panels=2,
            progress_callback=progress_callback
        )
        end_time = time.time()
        
        print(f'\n✅ 工作流完成，耗时: {end_time - start_time:.2f}秒')
        print(f'📈 总共收到 {len(progress_updates)} 个进度更新')
        
        # 显示所有进度更新
        print('\n📋 详细进度列表:')
        for i, (node, status, data, timestamp) in enumerate(progress_updates):
            relative_time = timestamp - start_time
            print(f'  {i+1:2d}. [{relative_time:6.2f}s] {node} -> {status}')
        
        # 分析节点覆盖情况
        expected_nodes = [
            'input_handler',
            'story_splitter', 
            'character_extractor',
            'storyboard_generator',
            'scene_prompt_optimizer',
            'unified_image_generator',
            'image_merger',
            'finalize_illustration'
        ]
        
        print(f'\n🔍 节点执行分析:')
        executed_nodes = set(node for node, _, _, _ in progress_updates)
        for node in expected_nodes:
            status = '✅' if node in executed_nodes else '❌'
            print(f'  {status} {node}')
        
        # 检查每个节点是否都有开始和完成状态
        print(f'\n📊 节点状态完整性检查:')
        node_statuses = {}
        for node, status, _, _ in progress_updates:
            if node not in node_statuses:
                node_statuses[node] = []
            node_statuses[node].append(status)
        
        for node in expected_nodes:
            if node in node_statuses:
                statuses = node_statuses[node]
                has_start = 'in_progress' in statuses
                has_complete = 'completed' in statuses or 'error' in statuses
                status_icon = '✅' if has_start and has_complete else '⚠️'
                print(f'  {status_icon} {node}: {statuses}')
            else:
                print(f'  ❌ {node}: 未执行')
                
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_full_workflow_progress())
