#!/usr/bin/env python3
"""
测试新功能：分镜数量自动判断和图片处理并发控制
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from agent.illustration_nodes import _determine_optimal_panel_count
from agent.configuration import Configuration
from agent.async_image_manager import AsyncImageManager


def test_panel_count_determination():
    """测试分镜数量自动判断功能"""
    print("=== 测试分镜数量自动判断功能 ===")
    
    # 创建配置对象
    config = Configuration()
    
    # 测试用例
    test_cases = [
        {
            "story": "小猫咪在花园里玩耍。",
            "expected_range": (1, 3),
            "description": "短故事"
        },
        {
            "story": "小白兔走进森林，遇到了一只蝴蝶。然后它们一起玩耍，最后成为了好朋友。",
            "expected_range": (2, 6),
            "description": "中等故事"
        },
        {
            "story": """从前有一只小狐狸，住在深山里。一天，它决定去探索外面的世界。
            首先，它来到了一个美丽的湖边，看到了自己的倒影。
            接着，它遇到了一群友善的小鸟，它们告诉狐狸关于人类村庄的故事。
            然后，小狐狸决定前往村庄看看。在路上，它帮助了一只受伤的小鹿。
            最后，小狐狸到达了村庄，发现人类并不可怕，还有很多有趣的东西。
            从此以后，小狐狸经常在森林和村庄之间穿梭，成为了连接两个世界的桥梁。""",
            "expected_range": (6, 15),
            "description": "长故事"
        },
        {
            "story": "",
            "expected_range": (4, 4),  # 应该返回默认值
            "description": "空故事"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {case['description']}")
        print(f"故事内容: {case['story'][:50]}{'...' if len(case['story']) > 50 else ''}")
        
        result = _determine_optimal_panel_count(case['story'], config)
        min_expected, max_expected = case['expected_range']
        
        print(f"建议分镜数量: {result}")
        print(f"期望范围: {min_expected}-{max_expected}")
        
        if min_expected <= result <= max_expected:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")


async def test_concurrent_control():
    """测试图片处理并发控制"""
    print("\n=== 测试图片处理并发控制 ===")
    
    # 创建异步图片管理器，限制并发数为3（用于测试）
    manager = AsyncImageManager(max_concurrent_tasks=3)
    
    print(f"最大并发任务数: {manager.max_concurrent_tasks}")
    print(f"信号量初始值: {manager.semaphore._value}")
    
    # 模拟多个任务同时请求
    async def mock_task(task_id: int, duration: float):
        print(f"任务 {task_id} 开始等待信号量...")
        async with manager.semaphore:
            print(f"任务 {task_id} 获得信号量，开始执行")
            await asyncio.sleep(duration)
            print(f"任务 {task_id} 执行完成，释放信号量")
        return f"任务 {task_id} 结果"
    
    # 创建6个任务，但只能同时执行3个
    tasks = [
        mock_task(1, 1.0),
        mock_task(2, 0.5),
        mock_task(3, 1.5),
        mock_task(4, 0.8),
        mock_task(5, 1.2),
        mock_task(6, 0.3)
    ]
    
    print(f"\n创建 {len(tasks)} 个任务，但最多同时执行 {manager.max_concurrent_tasks} 个")
    
    start_time = asyncio.get_event_loop().time()
    results = await asyncio.gather(*tasks)
    end_time = asyncio.get_event_loop().time()
    
    print(f"\n所有任务完成，总耗时: {end_time - start_time:.2f} 秒")
    print(f"任务结果: {results}")
    print("✅ 并发控制测试完成")


def test_api_validation():
    """测试API验证范围"""
    print("\n=== 测试API验证范围 ===")
    
    from pydantic import ValidationError
    from agent.app import IllustrationRequest
    
    # 测试有效的分镜数量
    valid_cases = [0, 1, 5, 10, 15, 20]
    for num in valid_cases:
        try:
            request = IllustrationRequest(
                user_input="测试故事",
                num_panels=num
            )
            print(f"✅ num_panels={num} 验证通过")
        except ValidationError as e:
            print(f"❌ num_panels={num} 验证失败: {e}")
    
    # 测试无效的分镜数量
    invalid_cases = [-1, 21, 25, 100]
    for num in invalid_cases:
        try:
            request = IllustrationRequest(
                user_input="测试故事",
                num_panels=num
            )
            print(f"❌ num_panels={num} 应该验证失败但通过了")
        except ValidationError:
            print(f"✅ num_panels={num} 正确验证失败")


async def main():
    """主测试函数"""
    print("开始测试新功能...")
    
    # 测试分镜数量自动判断
    test_panel_count_determination()
    
    # 测试API验证
    test_api_validation()
    
    # 测试并发控制
    await test_concurrent_control()
    
    print("\n=== 所有测试完成 ===")


if __name__ == "__main__":
    asyncio.run(main())
