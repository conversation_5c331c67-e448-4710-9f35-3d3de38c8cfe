# Frontend Model Selection Test

## Test Steps

1. **Start the frontend application**
   ```bash
   cd frontend
   npm run dev
   ```

2. **Verify Model Selection UI**
   - Open the application in browser
   - Check that there are now 3 configuration sections:
     - 艺术风格 (Art Style)
     - 分镜数量 (Panel Count) 
     - 多图生图模型 (Multi-Edit Model) ← **NEW**

3. **Test Model Options**
   - Click on the "多图生图模型" dropdown
   - Verify two options are available:
     - ⚡ Flux模型 (推荐) - 最多4张
     - 🔮 Gemini 2.5 - 最多8张

4. **Test Default Selection**
   - Verify that Flux模型 is selected by default
   - Verify it shows "推荐" (Recommended) badge

5. **Test Model Switching**
   - Select Gemini 2.5 model
   - Verify the selection changes
   - Verify the "最多8张" (Max 8 images) badge is visible

6. **Test Form Submission**
   - Enter a test story
   - Select different models
   - Submit the form
   - Check browser network tab to verify the `multi_edit_model_id` field is included in the request

## Expected Request Format

When submitting with Gemini 2.5 selected, the request should include:

```json
{
  "user_input": "test story",
  "style_preference": "anime",
  "num_panels": 4,
  "multi_edit_model_id": "gemini25-nq2c-1cff-gg65-25gt5f8g6rjg"
}
```

When submitting with Flux selected (default):

```json
{
  "user_input": "test story", 
  "style_preference": "anime",
  "num_panels": 4,
  "multi_edit_model_id": "flux3dc-cff2-4177-ad3a-28d9b4d3ff48"
}
```

## Test Results

- [ ] Model selection UI appears correctly
- [ ] Both model options are available
- [ ] Default selection is Flux model
- [ ] Model switching works
- [ ] Request includes correct model_id
- [ ] Backend receives and processes model_id correctly

## Notes

The model selection specifically affects multi-image generation (when characters are involved in scenes). For simple text-to-image generation without character references, the model selection may not have a visible effect, but the parameter should still be passed through the system correctly.
